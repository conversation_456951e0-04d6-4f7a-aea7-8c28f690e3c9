import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsNotEmpty,
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsUUID,
} from "class-validator";
import { PayoutStatus } from "@shared/types";
import { PaymentMethod } from "../../payments/enums/payment-method.enum";

export class CreatePayoutDto {
  @ApiProperty({
    description: "Worker ID who will receive the payout",
    example: "550e8400-e29b-41d4-a716-************",
  })
  @IsUUID()
  @IsNotEmpty()
  workerId: string;

  @ApiProperty({
    description: "Job ID associated with this payout",
    example: "550e8400-e29b-41d4-a716-************",
  })
  @IsUUID()
  @IsNotEmpty()
  jobId: string;

  @ApiProperty({
    description: "Amount to be paid in smallest currency unit (e.g., cents)",
    example: 5000,
  })
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiPropertyOptional({
    description: "Currency code (ISO 4217)",
    example: "USD",
    default: "USD",
  })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiPropertyOptional({
    description: "Description of the payout",
    example: "Payout for job completion",
  })
  @IsString()
  @IsOptional()
  description?: string;
}

export class UpdatePayoutDto {
  @ApiPropertyOptional({
    description: "Status of the payout",
    enum: PayoutStatus,
    example: PayoutStatus.PROCESSING,
  })
  @IsEnum(PayoutStatus)
  @IsOptional()
  status?: PayoutStatus;

  @ApiPropertyOptional({
    description: "Transaction ID from payment processor",
    example: "txn_1234567890",
  })
  @IsString()
  @IsOptional()
  transactionId?: string;

  @ApiPropertyOptional({
    description: "Notes about the payout",
    example: "Processed manually due to system issue",
  })
  @IsString()
  @IsOptional()
  notes?: string;
}

export class ProcessPayoutDto {
  @ApiProperty({
    description: "Payment method to use for processing the payout",
    enum: PaymentMethod,
    example: PaymentMethod.STRIPE,
  })
  @IsEnum(PaymentMethod)
  @IsNotEmpty()
  paymentMethod!: PaymentMethod;
}

export class PayoutResponseDto {
  @ApiProperty({
    description: "Unique identifier for the payout",
    example: "550e8400-e29b-41d4-a716-************",
  })
  id!: string;

  @ApiProperty({
    description: "Worker ID who will receive the payout",
    example: "550e8400-e29b-41d4-a716-************",
  })
  workerId!: string;

  @ApiProperty({
    description: "Job ID associated with this payout",
    example: "550e8400-e29b-41d4-a716-************",
  })
  jobId!: string;

  @ApiProperty({
    description: "Amount to be paid in smallest currency unit (e.g., cents)",
    example: 5000,
  })
  amount!: number;

  @ApiProperty({
    description: "Currency code (ISO 4217)",
    example: "USD",
  })
  currency!: string;

  @ApiProperty({
    description: "Status of the payout",
    enum: PayoutStatus,
    example: PayoutStatus.PENDING,
  })
  status!: PayoutStatus;

  @ApiPropertyOptional({
    description: "Transaction ID from payment processor",
    example: "txn_1234567890",
  })
  transactionId?: string;

  @ApiProperty({
    description: "Creation date of the payout",
    example: "2023-01-01T00:00:00.000Z",
  })
  createdAt!: Date;

  @ApiProperty({
    description: "Last update date of the payout",
    example: "2023-01-01T00:00:00.000Z",
  })
  updatedAt!: Date;

  @ApiPropertyOptional({
    description: "Description of the payout",
    example: "Payout for job completion",
  })
  description?: string;

  @ApiPropertyOptional({
    description: "Notes about the payout",
    example: "Processed manually due to system issue",
  })
  notes?: string;
}
