{"name": "backend", "version": "1.0.0", "private": true, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "debug": "nest start --debug --watch", "prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage --coverageReporters=text-lcov --coverageReporters=html", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:ci": "jest --coverage --watchAll=false --passWithNoTests", "typeorm": "typeorm-ts-node-commonjs", "migration:create": "ts-node -r tsconfig-paths/register src/scripts/migration-create.ts", "migration:run": "ts-node -r tsconfig-paths/register src/scripts/migration-run.ts", "migration:revert": "ts-node -r tsconfig-paths/register src/scripts/migration-revert.ts", "migration:status": "ts-node -r tsconfig-paths/register src/scripts/migration-status.ts"}, "dependencies": {"@aws-sdk/client-ses": "^3.812.0", "@aws-sdk/credential-provider-node": "^3.812.0", "@neondatabase/serverless": "^0.6.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.0.0", "@nestjs/jwt": "^10.1.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/schedule": "^3.0.1", "@nestjs/swagger": "^7.1.1", "@nestjs/terminus": "^10.3.0", "@nestjs/throttler": "^4.2.1", "@nestjs/typeorm": "^10.0.0", "@nestjs/websockets": "^10.4.17", "@sentry/node": "^7.120.3", "@sentry/profiling-node": "^1.3.5", "@types/multer": "^1.4.12", "bcrypt": "^5.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.0.1", "firebase-admin": "^13.4.0", "helmet": "^7.0.0", "ioredis": "^5.3.2", "nestjs-zod": "^2.3.3", "nodemailer": "^6.10.1", "nodemailer-mailgun-transport": "^2.1.5", "nodemailer-sendgrid": "^1.0.3", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "pg": "^8.11.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "sanitize-html": "^2.16.0", "shared": "*", "socket.io": "^4.8.1", "razorpay": "^2.9.4", "stripe": "^18.1.0", "typeorm": "^0.3.17", "uuid": "^9.0.1", "zod": "^3.21.4"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.0", "@types/compression": "^1.7.5", "@types/dotenv": "^8.2.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.14", "@types/passport-jwt": "^3.0.9", "@types/sanitize-html": "^2.16.0", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s", "!**/*.spec.ts", "!**/*.e2e-spec.ts", "!**/node_modules/**", "!**/dist/**", "!**/coverage/**", "!**/*.interface.ts", "!**/*.dto.ts", "!**/*.entity.ts", "!**/main.ts"], "coverageDirectory": "../coverage", "testEnvironment": "node", "coverageReporters": ["text", "lcov", "html"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}, "setupFilesAfterEnv": ["<rootDir>/../test/setup.ts"], "moduleNameMapping": {"^src/(.*)$": "<rootDir>/$1"}}}